<!doctype html>
<html>
  <body>
    <div id="container"></div>
    <script type="module">
      // don't import minified CSS files (e.g. `*.min.css`), since that actually
      // makes the dist/index.html slightly larger!
      import "@fortawesome/fontawesome-free/css/brands.css"
      import "@fortawesome/fontawesome-free/css/regular.css"
      import "@fortawesome/fontawesome-free/css/solid.css"
      import "@fortawesome/fontawesome-free/css/fontawesome.css"
      import "katex/dist/katex.css"

      import elkLayouts from "@mermaid-js/layout-elk"

      // expose elkLayouts to the global scope so that puppeteer can see it
      globalThis.elkLayouts = elkLayouts
    </script>
  </body>
</html>
