FROM node:18.20-alpine3.19 AS build

ENV CHROME_BIN="/usr/bin/chromium-browser" \
    PUPPETEER_SKIP_DOWNLOAD="true"

RUN apk add chromium

WORKDIR /app

COPY . /app/

RUN npm install \
    && npm pack

FROM node:18.20-alpine3.19 AS mermaid-cli-current

WORKDIR /app

COPY --from=build /app/*-mermaid-cli-*.tgz /install/
COPY --from=build /app/puppeteer-config.json /puppeteer-config.json

ADD install-dependencies.sh install-dependencies.sh
RUN chmod 755 install-dependencies.sh && /bin/sh install-dependencies.sh

RUN adduser -D mermaidcli
USER mermaidcli
WORKDIR /home/<USER>
RUN npm install $(ls -d /install/*.tgz)

ADD puppeteer-config.json  /puppeteer-config.json
WORKDIR /data
ENTRYPOINT ["/home/<USER>/node_modules/.bin/mmdc", "-p", "/puppeteer-config.json"]
C<PERSON> [ "--help" ]
