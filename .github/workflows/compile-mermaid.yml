name: "Build, test and deploy mermaid-cli Docker image"
on: [push, pull_request]
concurrency: ci-${{ github.ref }}
jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 90

    env:
      DOCKERFILE: DockerfileBuild
      IMAGENAME: mermaid-cli
      DOCKER_IO_REPOSITORY: minlag/mermaid-cli
      INPUT_DATA: test-positive
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install GitVersion
        uses: gittools/actions/gitversion/setup@v3.2.1
        with:
          versionSpec: "5.x"

      - name: Use GitVersion
        id: gitversion # step id used as reference for output values
        uses: gittools/actions/gitversion/execute@v3.2.1

      - name: Convert repository name to lower case
        run: echo "GITHUB_REPOSITORY_LOWER_CASE=$(echo $GITHUB_REPOSITORY | tr '[:upper:]' '[:lower:]')" >> $GITHUB_ENV

      - name: Get release version
        run: echo "RELEASE_VERSION=${{ steps.gitversion.outputs.semVer }}" >> $GITHUB_ENV

      - name: Create image tag
        run: echo "IMAGETAG=${{env.GITHUB_REPOSITORY_LOWER_CASE}}/$IMAGENAME:${{env.RELEASE_VERSION}}" >> $GITHUB_ENV

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Test Image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ${{env.DOCKERFILE}}
          load: true
          tags: "${{env.IMAGETAG}}"

      - name: Run e2e tests
        run: bash run-tests.sh ${{env.INPUT_DATA}} ${{env.IMAGETAG}}
        timeout-minutes: 5

      - name: Convert all svg files to png before uploading for automatic inspection
        run: |
          ls -la "$INPUT_DATA"
          svg_files_to_convert=(
            "$INPUT_DATA"/graph-with-br.mmd.svg
            "$INPUT_DATA"/graph-with-br.mmd-stdin.svg
            # svg file is named `-svg` so it doesn't overwrite .png file
            "$INPUT_DATA"/flowchart1-red-background-svg.svg
            "$INPUT_DATA"/flowchart1-with-css-svg.svg
          )
          # This will overwrite any PNG files with the same name that have been created by run-tests.sh
          # v0.5.0 is the last version to correctly convert our SVG files to PNG
          # We use aa-exec since `convert-svg-to-png` uses puppeteer, which AppArmor blocks in Ubuntu 24.04
          aa-exec --profile=chrome npx --yes convert-svg-to-png@0.5.0 "${svg_files_to_convert[@]}"

      - name: Upload diagrams for manual inspection
        # also uploads for `upload-percy.yml` action
        uses: actions/upload-artifact@v4.6.2
        with:
          name: output
          path: ./${{env.INPUT_DATA}}
      - name: Upload diagrams to percy for automatic inspection
        # only run on push
        # For PRs, the ./upload-percy.yml file is used instead
        if: github.event_name == 'push'
        # copied from https://docs.percy.io/docs/github-actions
        # should automatically upload `.png`
        run: npx @percy/cli upload "$INPUT_DATA"
        env:
          PERCY_TOKEN: ${{ secrets.PERCY_TOKEN }}

      - name: Login to GitHub Container Registry
        if: github.ref == 'refs/heads/master'
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Login to DockerHub
        if: github.ref == 'refs/heads/master'
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and Push Image(s)
        if: github.ref == 'refs/heads/master'
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ${{env.DOCKERFILE}}
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            ${{env.DOCKER_IO_REPOSITORY}}:latest
            ${{env.DOCKER_IO_REPOSITORY}}:${{env.RELEASE_VERSION}}
            ghcr.io/${{env.GITHUB_REPOSITORY_LOWER_CASE}}/${{env.IMAGENAME}}:latest
            ghcr.io/${{env.GITHUB_REPOSITORY_LOWER_CASE}}/${{env.IMAGENAME}}:${{env.RELEASE_VERSION}}
