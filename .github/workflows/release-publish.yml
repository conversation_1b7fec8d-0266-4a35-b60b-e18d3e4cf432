name: Publish release

on:
  release:
    types: [published]

jobs:
  publish:
    runs-on: ubuntu-latest

    env:
      DOCKERFILE: Dockerfile
      IMAGENAME: mermaid-cli
      DOCKER_IO_REPOSITORY: minlag/mermaid-cli

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: fregante/setup-git-user@v2

      - uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: npm
          registry-url: https://registry.npmjs.org/
      - name: Build the package
        # throws an error if package-lock.json is out-of-date
        # `npm ci` automatically builds the package
        run: npm ci
      - name: Install GitVersion
        uses: gittools/actions/gitversion/setup@v3.2.1
        with:
          versionSpec: "5.x"

      - name: Use GitVersion
        id: gitversion # step id used as reference for output values
        uses: gittools/actions/gitversion/execute@v3.2.1

      - name: Get release version
        run: echo "RELEASE_VERSION=${{ steps.gitversion.outputs.semVer }}" >> $GITHUB_ENV

      - name: Prepare release
        run: npm version --no-git-tag-version --allow-same-version ${{env.RELEASE_VERSION}}

      - name: Convert repository name to lower case
        run: echo "GITHUB_REPOSITORY_LOWER_CASE=$(echo $GITHUB_REPOSITORY | tr '[:upper:]' '[:lower:]')" >> $GITHUB_ENV

      - name: Publish to npmjs
        run: npm publish --access public
        env:
          NODE_AUTH_TOKEN: ${{secrets.NPM_TOKEN}}

      - name: The job has failed
        if: ${{ failure() }}
        uses: actions/upload-artifact@v4.6.2
        with:
          name: npm-logs
          path: /home/<USER>/.npm/_logs

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and Push Image(s)
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ${{env.DOCKERFILE}}
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            ${{env.DOCKER_IO_REPOSITORY}}:latest
            ${{env.DOCKER_IO_REPOSITORY}}:${{env.RELEASE_VERSION}}
            ghcr.io/${{env.GITHUB_REPOSITORY_LOWER_CASE}}/${{env.IMAGENAME}}:latest
            ghcr.io/${{env.GITHUB_REPOSITORY_LOWER_CASE}}/${{env.IMAGENAME}}:${{env.RELEASE_VERSION}}

      - name: Post deployment tests
        run: |
          for i in $(ls test-positive/*.mmd); do docker run -v $(pwd):/data ${{env.DOCKER_IO_REPOSITORY}}:${{env.RELEASE_VERSION}} -i /data/$i; done
          for i in $(ls test-positive/*.mmd); do cat $i | docker run -i -v $(pwd):/data ${{env.DOCKER_IO_REPOSITORY}}:${{env.RELEASE_VERSION}} -o /data/$i-stdin.svg; done

      - name: Commit new version to the repository
        run: |
          git add package.json
          git checkout master
          git commit -m "Bump version ${{env.RELEASE_VERSION}}"
          git push --no-verify

      # For manual inspection of the generated artifacts
      - uses: actions/upload-artifact@v4.6.2
        with:
          name: output
          path: ./test-positive
