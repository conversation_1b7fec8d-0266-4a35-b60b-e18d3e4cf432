# Number of days of inactivity before an issue becomes stale
daysUntilStale: 60
# Number of days of inactivity before a stale issue is closed
daysUntilClose: 14
# Issues with these labels will never be considered stale
exemptLabels:
  - Retained
# Label to use when marking an issue as stale
staleLabel: Inactive
# Comment to post when marking an issue as stale. Set to `false` to disable
markComment: >
  This issue has been automatically marked as stale because it has not had
  recent activity. It will be closed if no further activity occurs. Thank you
  for your contributions.
  If you are still interested in this issue and it is still relevant you can comment to revive it.
# Comment to post when closing a stale issue. Set to `false` to disable
closeComment: >
  This issue has been automatically closed due to a lack of activity. 
  This is done to maintain a clean list of issues that the community is interested in developing. 
